"""
Tests for apartment download functionality.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from src.models.apartment_download_request import ApartmentDownloadRequest
from src.models.apartment_download_result import ApartmentDownloadResult
from src.services.apartment_download_service import ApartmentDownloadService


class TestApartmentDownloadRequest:
    """Test cases for ApartmentDownloadRequest model."""

    def test_valid_booking_url(self):
        """Test that valid Booking.com URLs are accepted."""
        valid_urls = [
            "https://www.booking.com/hotel/gr/test-apartment.html",
            "https://booking.com/hotel/us/another-place.html",
            "https://www.booking.com/hotel/fr/paris-apartment.html?aid=123",
        ]
        
        for url in valid_urls:
            request = ApartmentDownloadRequest(url=url)
            assert request.url == url

    def test_invalid_urls(self):
        """Test that invalid URLs are rejected."""
        invalid_urls = [
            "https://www.google.com",
            "https://www.airbnb.com/rooms/123",
            "https://www.booking.com/searchresults.html",
            "https://www.booking.com/",
            "",
        ]
        
        for url in invalid_urls:
            with pytest.raises(ValueError):
                ApartmentDownloadRequest(url=url)

    def test_get_apartment_id_from_query_param(self):
        """Test apartment ID extraction from query parameters."""
        url = "https://www.booking.com/hotel/gr/test.html?hotel_id=12345"
        request = ApartmentDownloadRequest(url=url)
        assert request.get_apartment_id() == "12345"

    def test_get_apartment_id_from_path(self):
        """Test apartment ID extraction from URL path."""
        url = "https://www.booking.com/hotel/gr/amazing-apartment.html"
        request = ApartmentDownloadRequest(url=url)
        assert request.get_apartment_id() == "amazing-apartment"

    def test_get_sub_directory(self):
        """Test subdirectory path generation."""
        url = "https://www.booking.com/hotel/gr/test-apartment.html"
        request = ApartmentDownloadRequest(url=url)
        assert request.get_sub_directory() == "apartments/test-apartment"

    def test_get_display_name(self):
        """Test display name generation."""
        url = "https://www.booking.com/hotel/gr/test-apartment.html"
        request = ApartmentDownloadRequest(url=url)
        assert request.get_display_name() == "Apartment test-apartment"


class TestApartmentDownloadResult:
    """Test cases for ApartmentDownloadResult model."""

    def test_successful_result(self):
        """Test creation of successful result."""
        result = ApartmentDownloadResult(
            url="https://www.booking.com/hotel/gr/test.html",
            apartment_id="test",
            success=True,
            output_path="/path/to/file.html",
            elapsed_time=10.5
        )
        
        assert result.success is True
        assert result.output_path == "/path/to/file.html"
        assert result.elapsed_time == 10.5
        assert result.error is None

    def test_failed_result(self):
        """Test creation of failed result."""
        result = ApartmentDownloadResult(
            url="https://www.booking.com/hotel/gr/test.html",
            apartment_id="test",
            success=False,
            output_path=None,
            elapsed_time=5.0,
            error="Connection timeout"
        )
        
        assert result.success is False
        assert result.output_path is None
        assert result.error == "Connection timeout"

    def test_to_dict(self):
        """Test conversion to dictionary."""
        result = ApartmentDownloadResult(
            url="https://www.booking.com/hotel/gr/test.html",
            apartment_id="test",
            success=True,
            output_path="/path/to/file.html",
            elapsed_time=10.5
        )
        
        expected = {
            "url": "https://www.booking.com/hotel/gr/test.html",
            "apartment_id": "test",
            "success": True,
            "output_path": "/path/to/file.html",
            "elapsed_time": 10.5,
            "error": None
        }
        
        assert result.to_dict() == expected


class TestApartmentDownloadService:
    """Test cases for ApartmentDownloadService."""

    @pytest.fixture
    def service(self):
        """Create a service instance for testing."""
        return ApartmentDownloadService()

    @pytest.fixture
    def mock_request(self):
        """Create a mock request for testing."""
        return ApartmentDownloadRequest(url="https://www.booking.com/hotel/gr/test.html")

    def test_service_initialization(self, service):
        """Test that service initializes correctly."""
        assert service.factory is not None
        assert service.download_statuses == []

    def test_get_download_statuses_empty(self, service):
        """Test getting statuses when none exist."""
        statuses = service.get_download_statuses()
        assert statuses == []

    def test_clear_download_statuses(self, service):
        """Test clearing download statuses."""
        # Add a mock status
        service.download_statuses.append({"test": "status"})
        assert len(service.download_statuses) == 1
        
        service.clear_download_statuses()
        assert len(service.download_statuses) == 0
