# Changelog

All notable changes to the Searcher Service project are documented in this file.

## [Latest] - 2025-06-01

### Added

#### Apartment Download Feature
- **New CLI Command**: `download-apartment` for downloading individual apartment pages
  - URL validation ensuring only valid Booking.com apartment URLs
  - Custom output directory support with `--output-dir` option
  - Progress reporting and comprehensive error handling
  - Apartment ID extraction from URLs (query parameters or path)

- **New API Endpoints**:
  - `POST /api/v1/download-apartment`: Start apartment download with background processing
  - `GET /api/v1/download-apartment/status`: Get status of all apartment downloads

- **New Data Models**:
  - `ApartmentDownloadRequest`: Type-safe request model with URL validation
  - `ApartmentDownloadResult`: Structured result object with comprehensive metrics
  - `ApartmentDownloadStatus`: Status tracking for apartment downloads

- **New Services**:
  - `ApartmentScraper`: Browser automation for apartment page scraping
  - `ApartmentDownloadService`: Orchestration and status tracking for downloads

#### Enhanced Architecture
- **Factory Pattern Extension**: `BrowserScrapperFactory` now supports apartment scraper creation
- **Dual Format Storage**: Apartment downloads save both HTML (raw) and JSON (parsed) formats
- **Integrated Data Extraction**: Reuses existing `ApartmentParser` for consistent data processing
- **Status Tracking**: Real-time progress monitoring for apartment downloads

#### Comprehensive Testing
- **Test Suite**: 12 new test cases covering all apartment download functionality
- **URL Validation Tests**: Comprehensive validation for valid and invalid URLs
- **Service Integration Tests**: End-to-end testing of download workflow
- **Error Handling Tests**: Robust error scenario coverage

#### Documentation Updates
- **README.md**: Complete documentation of apartment download feature
- **API.md**: New endpoints and data models documentation
- **APARTMENT_DOWNLOAD_FEATURE.md**: Detailed implementation documentation

### Enhanced

#### CLI Interface
- **Extended Command Set**: Now includes `serve`, `search`, `search-period`, and `download-apartment`
- **Consistent Error Handling**: Unified error reporting across all commands
- **Progress Reporting**: Enhanced user feedback for all operations

#### API Capabilities
- **Background Processing**: All operations support async background execution
- **Status Monitoring**: Comprehensive status tracking for searches and downloads
- **Type Safety**: Full Pydantic model integration for all endpoints

#### Storage Organization
- **Structured Output**: Apartment downloads organized in `apartments/{apartment_id}/` directories
- **Timestamp-based Naming**: Consistent file naming with timestamp prefixes
- **Multi-format Support**: HTML and JSON files for comprehensive data preservation

### Technical Details

#### New File Structure
```
src/models/
├── apartment_download_request.py    # New
└── apartment_download_result.py     # New

src/services/
├── apartment_scraper.py             # New
└── apartment_download_service.py    # New

tests/
└── test_apartment_download.py       # New
```

#### URL Validation Rules
- Must be from booking.com domain
- Must contain '/hotel/' in path
- Supports both www and non-www variants
- Handles query parameters and fragments

#### Data Extraction
- Apartment rating extraction
- Property features and amenities
- Size and bedroom information
- Facility availability (washing machine, kitchen, etc.)

## [Previous] - 2025-01-27

### Updated

#### Documentation Comprehensive Overhaul
- **API.md**: Enhanced with `elapsed_time` field in SearchStatus model and updated all example responses
- **README.md**: Major update with current implementation status and enhanced features
  - Added structured result objects documentation (SearchResult, PeriodSearchResult)
  - Enhanced timeout configuration details (page timeout, click timeout)
  - Updated performance monitoring capabilities
  - Added comprehensive API model documentation with examples
  - Updated all JSON response examples to include `elapsed_time` field
  - Enhanced features list with latest capabilities
- **Service Status**: Updated to reflect production-ready status with comprehensive feature set

#### Enhanced Feature Documentation
- **Real-time Monitoring**: Documented elapsed time tracking for all operations
- **Structured Results**: Comprehensive documentation of SearchResult and PeriodSearchResult objects
- **Performance Metrics**: Detailed documentation of timing and success/failure tracking
- **Timeout Configuration**: Enhanced documentation of configurable timeouts for browser operations
- **Modern Tooling**: Updated documentation to highlight uv package manager benefits (10-100x faster than pip)

## [Previous] - 2025-01-27

### Fixed

#### SearchStatus Error Handling Bug Fix
- **Issue**: SearchStatus objects were not properly updated with error information when searches failed
- **Root Cause**: Index calculation bug in search_service.py where `status_id` was used as array index instead of `status_id - 1`
- **Fix**: Corrected index calculation by introducing separate `status_index` variable for array access
- **Impact**: SearchStatus objects now correctly show error messages in the `error` field when searches fail
- **Testing**: Added comprehensive test suite to verify error handling works correctly
- **API Response**: `/api/v1/search/status` endpoint now properly returns error information for failed searches

#### SearchStatus Model Enhancement
- **Error Field Initialization**: SearchStatus objects now initialize with empty `error` field to ensure consistent structure
- **Type Safety**: Maintained string type for `error` field as specified in Pydantic model
- **Elapsed Time**: Confirmed `elapsed_time` calculated property works correctly for both successful and failed searches

## [Previous] - 2025-05-25

### Added

#### API Response Models Enhancement
- **SearchStatus Pydantic Model**: Type-safe model for search status responses
  - Fields: `id`, `area`, `guests`, `period`, `status`, `started_at`, `completed_at`, `output_path`, `error`
  - All fields properly typed with string values for API consistency
  - Optional fields for `completed_at`, `output_path`, and `error`

- **SearchStatusResponse Model**: Structured response wrapper for status endpoint
  - Contains list of SearchStatus objects
  - Ensures type safety and validation for API responses

#### Real-time Search Status Tracking
- **Enhanced Status Management**: Search service now tracks detailed status information
  - Unique string IDs for each search operation
  - ISO timestamp tracking for start and completion times
  - Real-time status updates: `in_progress`, `completed`, `failed`
  - Error message capture for failed searches
  - Output path tracking for successful searches

### Fixed

#### API Validation Issues
- **FastAPI Response Validation**: Resolved validation errors in search status endpoint
  - Fixed type mismatches (integer to string conversions)
  - Eliminated None values causing validation failures
  - Proper handling of optional fields with empty strings as defaults

#### Data Type Consistency
- **String Standardization**: All API response fields now use consistent string types
  - Search IDs converted from integers to strings
  - Guest counts converted from integers to strings for API consistency
  - Timestamps use ISO format strings
  - File paths use string representation

### Changed

#### API Response Structure
- **Search Status Endpoint**: Now returns structured SearchStatusResponse objects
  - Improved from generic Dict types to proper Pydantic models
  - Better documentation and validation
  - Enhanced error handling and type safety

#### Service Layer Improvements
- **SearchService.get_search_statuses()**: Now returns List[SearchStatus] instead of raw dictionaries
  - Automatic conversion from internal storage to Pydantic models
  - Type-safe response generation
  - Consistent data structure across API and internal usage

### Improved

#### Developer Experience
- **Type Safety**: Full Pydantic model integration for all API interactions
- **API Documentation**: Automatic OpenAPI schema generation with proper model definitions
- **Error Handling**: Better error messages and validation feedback
- **Code Maintainability**: Cleaner separation between internal data and API responses

## [Previous] - 2025-01-27

### Added

#### Result Objects Enhancement
- **SearchResult Class**: Individual searches now return comprehensive result objects
  - Fields: `area`, `guests`, `period`, `success`, `output_path`, `elapsed_time`, `error`
  - Field order: Contextual fields (area, guests, period) appear first
  - Complete search context and performance metrics included
  - `to_dict()` method for JSON serialization

- **PeriodSearchResult Class**: Period searches return detailed summaries
  - Fields: `total_weeks`, `successful`, `failed`, `errors`, `elapsed_time`
  - Comprehensive batch processing statistics
  - Error aggregation and detailed reporting

#### Timeout Configuration System
- **Page Timeout**: New `BROWSER_PAGE_TIMEOUT_IN_MILLISECONDS` environment variable
  - Default: 30000ms (30 seconds)
  - Used for page loading, navigation, and general page operations
  - Implemented across all browser classes

- **Click Timeout**: Enhanced click operation timeout management
  - Renamed from `BROWSER_SELECTOR_TIMEOUT_IN_MILLISECONDS` to `BROWSER_CLICK_TIMEOUT_IN_MILLISECONDS`
  - Default: 5000ms (5 seconds)
  - Specific to click interactions and element interactions

- **Unified Implementation**: Consistent timeout configuration across:
  - `BrowserService`
  - `BookingBrowser`
  - `BrowserScrapperPlaywright`

#### Enhanced CLI Output
- **Detailed Success Messages**: Include elapsed time and output file path
- **Comprehensive Error Messages**: Show elapsed time and specific error details
- **Performance Metrics**: All operations report timing information

### Changed

#### Breaking Changes
- **Environment Variable Rename**: `BROWSER_SELECTOR_TIMEOUT_IN_MILLISECONDS` → `BROWSER_CLICK_TIMEOUT_IN_MILLISECONDS`
- **Search Service Return Type**: Changed from `str` (file path) to `SearchResult` object
- **Period Search Service Return Type**: Enhanced to include comprehensive result information

#### Default Values
- **Page Timeout**: New default of 30000ms for page operations
- **Click Timeout**: Increased from 500ms to 5000ms for better reliability
- **Field Order**: Result objects prioritize contextual information first

#### Service Architecture
- **SearchService**: Now returns structured `SearchResult` objects
- **PeriodSearchService**: Enhanced error handling and result aggregation
- **CLI Integration**: Updated to use new result objects for better user feedback

### Improved

#### Documentation
- **README.md**: Added comprehensive result object documentation with examples
- **DOCKER.md**: Enhanced with timeout configuration and performance monitoring sections
- **tests/README.md**: Updated testing guidelines for result objects and timeout configuration
- **config/README.md**: Added current environment variable documentation

#### Error Handling
- **Graceful Failures**: Search operations return result objects instead of throwing exceptions
- **Detailed Error Information**: Specific error messages with timing information
- **Batch Error Isolation**: Period searches continue processing despite individual failures

#### Performance Monitoring
- **Timing Metrics**: All operations include elapsed time measurements
- **Success/Failure Tracking**: Clear indication of operation outcomes
- **Batch Statistics**: Period searches provide comprehensive summaries

### Technical Details

#### Result Object Structure

**SearchResult:**
```json
{
  "area": "Psiri",
  "guests": 2,
  "period": "2025-06-01 to 2025-06-08",
  "success": true,
  "output_path": "/app/data/html/Psiri/2/2025-06-01_2025-06-08/20250127_103045.html",
  "elapsed_time": 27.34,
  "error": null
}
```

**PeriodSearchResult:**
```json
{
  "total_weeks": 4,
  "successful": 3,
  "failed": 1,
  "errors": ["Week 2/4 failed for 2025-06-08 to 2025-06-15: Connection timeout"],
  "elapsed_time": 89.67
}
```

#### Environment Variables

**New Variables:**
- `BROWSER_PAGE_TIMEOUT_IN_MILLISECONDS="30000"`

**Renamed Variables:**
- `BROWSER_SELECTOR_TIMEOUT_IN_MILLISECONDS` → `BROWSER_CLICK_TIMEOUT_IN_MILLISECONDS="5000"`

#### Migration Guide

**For Existing Deployments:**
1. Update environment variable: `BROWSER_SELECTOR_TIMEOUT_IN_MILLISECONDS` → `BROWSER_CLICK_TIMEOUT_IN_MILLISECONDS`
2. Review timeout values: new defaults may affect behavior
3. Update any code expecting string return values from search services
4. Test with new result object structure

**For API Consumers:**
- Background search tasks continue to work as before
- Search status endpoint now returns structured objects instead of generic dictionaries
- All response fields are now properly typed and validated
- Enhanced error handling provides better failure diagnostics
- No breaking changes to request formats or endpoint URLs

**For Developers:**
- Import new models: `SearchStatus`, `SearchStatusResponse` from `src.models.booking_search_request`
- Update type hints to use new Pydantic models
- Leverage improved type safety for better IDE support and error detection

### Dependencies

No changes to external dependencies. All enhancements use existing libraries and frameworks.

### Compatibility

- **Python**: Continues to support Python 3.8+
- **Docker**: Compatible with existing Docker configurations
- **API**: Background task endpoints remain unchanged
- **CLI**: Enhanced output while maintaining command compatibility

---

## Previous Versions

### [1.0.0] - Initial Release
- Basic search functionality
- Docker containerization
- CLI interface
- API endpoints
- Browser automation with Playwright
