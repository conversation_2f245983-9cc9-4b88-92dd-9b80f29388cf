"""
Model for apartment download requests.

This module defines the request model for downloading individual apartment pages
from Booking.com.
"""

import re
from urllib.parse import urlparse
from pydantic import BaseModel, Field, field_validator


class ApartmentDownloadRequest(BaseModel):
    """
    Model representing a request to download an apartment page from Booking.com.
    """

    url: str = Field(..., description="The Booking.com apartment URL to download")

    @field_validator('url')
    @classmethod
    def validate_booking_url(cls, v):
        """
        Validate that the URL is a valid Booking.com apartment URL.
        
        Args:
            v: The URL to validate
            
        Returns:
            str: The validated URL
            
        Raises:
            ValueError: If the URL is not a valid Booking.com apartment URL
        """
        if not v:
            raise ValueError("URL cannot be empty")
            
        # Parse the URL
        parsed = urlparse(v)
        
        # Check if it's a Booking.com domain
        if not parsed.netloc or 'booking.com' not in parsed.netloc.lower():
            raise ValueError("URL must be from booking.com domain")
            
        # Check if it looks like an apartment/hotel URL (contains /hotel/ path)
        if '/hotel/' not in parsed.path.lower():
            raise ValueError("URL must be a Booking.com property/apartment URL (should contain '/hotel/' in path)")
            
        return v

    def get_apartment_id(self) -> str:
        """
        Extract apartment/property ID from the URL.
        
        Returns:
            str: The apartment ID extracted from the URL
        """
        # Try to extract property ID from URL patterns like:
        # https://www.booking.com/hotel/gr/property-name.html?aid=123&hotel_id=456
        # or from the URL path
        
        # First try to get hotel_id from query parameters
        from urllib.parse import parse_qs
        parsed = urlparse(self.url)
        query_params = parse_qs(parsed.query)
        
        if 'hotel_id' in query_params:
            return query_params['hotel_id'][0]
            
        # If no hotel_id parameter, try to extract from path
        # Pattern like /hotel/gr/property-name.html
        path_match = re.search(r'/hotel/[^/]+/([^/.]+)', parsed.path)
        if path_match:
            return path_match.group(1)
            
        # Fallback: use a hash of the URL
        import hashlib
        return hashlib.md5(self.url.encode()).hexdigest()[:8]

    def get_sub_directory(self) -> str:
        """
        Get the subdirectory path for storing apartment download results.
        
        Returns:
            str: The subdirectory path
        """
        apartment_id = self.get_apartment_id()
        return f"apartments/{apartment_id}"

    def get_display_name(self) -> str:
        """
        Get a display name for the apartment download.
        
        Returns:
            str: A human-readable name for the download
        """
        apartment_id = self.get_apartment_id()
        return f"Apartment {apartment_id}"
