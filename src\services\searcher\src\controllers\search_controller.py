"""
Search Controller

This module defines the API endpoints for the searcher service.
"""

from datetime import datetime, timedelta
import os
from typing import Dict, Any
from fastapi import APIRouter, BackgroundTasks, HTTPException

from src.models.booking_search_request import (
    BookingSearchRequest,
    BookingSearchPeriodRequest,
    SearchStatusResponse,
)
from src.models.apartment_download_request import ApartmentDownloadRequest
from src.services.search_service import SearchService
from src.services.period_search_service import PeriodSearchService
from src.services.apartment_download_service import ApartmentDownloadService
from src.utils.common_utils import get_period_weeks

# Create router
router = APIRouter()

# Create search service
search_service = SearchService()

# Create apartment download service
apartment_download_service = ApartmentDownloadService()

# Default output directory from environment variable or use default
DEFAULT_OUTPUT_DIR = os.getenv("BROWSER_OUTPUT_DIR", "./data/html")


@router.post("/search", response_model=Dict[str, str])
async def search(request: BookingSearchRequest, background_tasks: BackgroundTasks):
    """
    Perform a booking.com search and save the results.

    The search is performed asynchronously in the background.

    Returns:
        Dict[str, str]: A dictionary with a message indicating the search has been started.
    """
    # Add the search task to background tasks
    if request.check_in <= datetime.today():
        request.check_in = datetime.today() + timedelta(days=1)
    if request.check_out <= request.check_in:
        request.check_out = request.check_in + timedelta(days=1)
    background_tasks.add_task(search_service.search, request, DEFAULT_OUTPUT_DIR)

    return {
        "message": f"Search started for {request.area} with {request.guests} guests for period {request.get_period_name()}"
    }


@router.post("/search-period", response_model=Dict[str, Any])
async def search_period(
    request: BookingSearchPeriodRequest, background_tasks: BackgroundTasks
):
    """
    Perform a booking.com period search across multiple weeks and save the results.

    The search is performed asynchronously in the background for all weeks in the specified period.

    Returns:
        Dict[str, str]: A dictionary with a message indicating the period search has been started.
    """
    # Calculate weeks for the period
    if request.date_from <= datetime.today():
        request.date_from = datetime.today() + timedelta(days=1)
    if request.date_upto <= request.date_from:
        request.date_upto = request.date_from + timedelta(days=1)
    weeks = get_period_weeks(request.date_from, request.date_upto)

    if len(weeks) == 0:
        raise HTTPException(
            status_code=400, detail="No weeks found in the specified date range"
        )

    first_week_start = weeks[0][0]
    if first_week_start <= datetime.today():
        first_week_start = datetime.today() + timedelta(days=1)
    weeks[0] = (first_week_start, weeks[0][1])

    # Create period search service and add task to background tasks
    period_search_service = PeriodSearchService(search_service)
    background_tasks.add_task(
        period_search_service.run_period_search,
        request.area,
        request.guests,
        weeks,
        DEFAULT_OUTPUT_DIR,
        request.max_concurrent,
        None,  # No progress callback for API
    )

    return {
        "message": f"Period search started for {request.area} with {request.guests} guests for period {request.get_period_name()}",
        "weeks_count": len(weeks),
        "max_concurrent": request.max_concurrent,
        "weeks": [
            {"start": start.strftime("%Y-%m-%d"), "end": end.strftime("%Y-%m-%d")}
            for start, end in weeks
        ],
    }


@router.post("/download-apartment", response_model=Dict[str, str])
async def download_apartment(request: ApartmentDownloadRequest, background_tasks: BackgroundTasks):
    """
    Download a specific apartment page from Booking.com.

    The download is performed asynchronously in the background.

    Returns:
        Dict[str, str]: A dictionary with a message indicating the download has been started.
    """
    # Add the download task to background tasks
    background_tasks.add_task(apartment_download_service.download_apartment, request, DEFAULT_OUTPUT_DIR)

    return {
        "message": f"Apartment download started for {request.get_display_name()}",
        "apartment_id": request.get_apartment_id(),
        "url": request.url
    }


@router.get("/download-apartment/status", response_model=Dict[str, Any])
async def get_apartment_download_status():
    """
    Get the status of all apartment downloads.

    Returns:
        Dict[str, Any]: A response containing a list of apartment download statuses.
    """
    try:
        statuses = apartment_download_service.get_download_statuses()
        return {"downloads": statuses}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/search/status", response_model=SearchStatusResponse)
async def get_search_status():
    """
    Get the status of all searches.

    Returns:
        SearchStatusResponse: A response containing a list of search statuses.
    """
    try:
        statuses = search_service.get_search_statuses()
        return SearchStatusResponse(searches=statuses)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
