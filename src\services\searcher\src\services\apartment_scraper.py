"""
Apartment Scraper Service

This module provides functionality for scraping individual apartment pages
from Booking.com.
"""

import json
import logging
import os
from typing import Optional

from src.models.apartment_download_request import ApartmentDownloadRequest
from src.parsers.apartment_parser import ApartmentParser
from src.services.browser_scrapper_abstract import B<PERSON><PERSON><PERSON><PERSON><PERSON>per<PERSON>bstract
from src.stores.browser_scrapper_store_abstract import Browser<PERSON>crapperStoreAbstract
from src.utils.common_utils import fix_windows_event_loop_policy

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ApartmentScraper(BrowserScrapperAbstract):
    """
    Scraper for individual Booking.com apartment pages.
    """

    def __init__(self, request: ApartmentDownloadRequest, store: BrowserScrapperStoreAbstract):
        """
        Initialize the apartment scraper.

        Args:
            request: The apartment download request
            store: The store to save the results to
        """
        super().__init__(request.url, store)
        self.request = request
        self.apartment_parser = ApartmentParser()

    async def download_page(self, sub_dir: str = None) -> str:
        """
        Download the apartment page and save it to the store.

        Args:
            sub_dir: The subdirectory to save the results to

        Returns:
            str: The path to the saved file
        """
        logger.info(f"Starting apartment download for {self.request.get_display_name()}")
        logger.info(f"URL: {self.request.url}")

        try:
            # Download the page content
            content = await self.download_page_content()

            # Save the content
            output_path = self._save_content(content, sub_dir)

            logger.info(f"Apartment download completed and saved to {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Error during apartment download: {e}")
            raise

    async def download_page_content(self) -> str:
        """
        Download the apartment page content using Playwright.

        Returns:
            str: The HTML content of the apartment page
        """
        # Fix Windows event loop policy before using Playwright
        fix_windows_event_loop_policy()

        from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
        import random

        # User agents for rotation
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]

        # Configure browser settings
        headless = os.getenv("BROWSER_HEADLESS", "false").lower() == "true"
        page_timeout = int(os.getenv("BROWSER_PAGE_TIMEOUT_IN_MILLISECONDS", "30000"))
        click_timeout = int(os.getenv("BROWSER_CLICK_TIMEOUT_IN_MILLISECONDS", "5000"))

        async with async_playwright() as p:
            # Launch browser
            browser = await p.chromium.launch(
                headless=headless,
                args=[
                    "--no-sandbox",
                    "--disable-blink-features=AutomationControlled",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                ]
            )

            # Create a new browser context with a random user agent
            user_agent = random.choice(user_agents)
            context = await browser.new_context(
                viewport={"width": 1280, "height": 800},
                user_agent=user_agent,
                locale="en-US",
                timezone_id="Europe/Athens",
                has_touch=False,
                java_script_enabled=True,
                ignore_https_errors=False,
                extra_http_headers={
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Connection": "keep-alive",
                },
            )

            # Add script to hide webdriver
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                });
            """)

            # Create a new page
            page = await context.new_page()

            try:
                # Navigate directly to the apartment URL
                logger.info(f"Navigating to apartment URL: {self.request.url}")
                await page.goto(self.request.url)
                await page.wait_for_load_state("networkidle", timeout=page_timeout)

                # Accept cookies if dialog appears
                try:
                    await page.click(
                        'button[id="onetrust-accept-btn-handler"]',
                        timeout=click_timeout,
                    )
                    logger.info("Accepted cookies")
                except PlaywrightTimeoutError:
                    logger.info("No cookie dialog found or already accepted")

                # Wait a bit for dynamic content to load
                await page.wait_for_timeout(2000)

                # Get page content
                content = await page.content()
                logger.info("Successfully retrieved apartment page content")

                return content

            finally:
                # Close browser
                await browser.close()

    def _save_content(self, content: str, sub_dir: str = None) -> str:
        """
        Save the content to the store with both HTML and JSON formats.

        Args:
            content: The HTML content to save
            sub_dir: The subdirectory to save the content to

        Returns:
            str: The path to the saved HTML file
        """
        # Save HTML content
        html_path = self.store.save_content(self.url, content, ".html", sub_dir)
        
        try:
            # Extract apartment data and save as JSON
            apartment_name = self.request.get_display_name()
            apartment_data = self.apartment_parser.parse_apartment_page(content, apartment_name)
            
            # Convert DataFrame to dict for JSON serialization
            if not apartment_data.empty:
                json_data = apartment_data.to_dict('records')[0]
            else:
                json_data = {"error": "No apartment data could be extracted"}
                
            json_content = json.dumps(json_data, indent=2, ensure_ascii=False)
            self.store.save_content(self.url, json_content, ".json", sub_dir)
            
            logger.info("Successfully saved both HTML and JSON content")
            
        except Exception as e:
            logger.warning(f"Failed to extract and save JSON data: {e}")
            # Continue anyway, we still have the HTML
        
        return html_path
