"""
Model for apartment download results.

This module defines the result model for apartment download operations.
"""

from typing import Optional, Dict, Any


class ApartmentDownloadResult:
    """Result of an apartment download operation."""

    def __init__(
        self, 
        url: str, 
        apartment_id: str, 
        success: bool, 
        output_path: Optional[str], 
        elapsed_time: float, 
        error: Optional[str] = None
    ):
        """
        Initialize the apartment download result.
        
        Args:
            url: The apartment URL that was downloaded
            apartment_id: The extracted apartment ID
            success: Whether the download was successful
            output_path: Path to the saved file (if successful)
            elapsed_time: Time taken for the download in seconds
            error: Error message (if failed)
        """
        self.url = url
        self.apartment_id = apartment_id
        self.success = success
        self.output_path = output_path
        self.elapsed_time = elapsed_time
        self.error = error

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary format.
        
        Returns:
            Dict[str, Any]: Dictionary representation of the result
        """
        return {
            "url": self.url,
            "apartment_id": self.apartment_id,
            "success": self.success,
            "output_path": self.output_path,
            "elapsed_time": self.elapsed_time,
            "error": self.error
        }
