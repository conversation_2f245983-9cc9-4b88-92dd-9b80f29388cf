# Docker Setup for Searcher Service

This document provides instructions for running the Searcher Service using Docker.

## Prerequisites

- Docker installed on your system
- Basic understanding of Docker commands

## Configuration

The service can be configured using environment variables when running the Docker container or by creating a `.env` file in the project root.

### Environment Variables

Key environment variables include:

| Variable | Description | Default |
|----------|-------------|---------|
| PYTHONPATH | Python path for module imports | /app |
| BROWSER_HEADLESS | Run browser in headless mode | false |
| BROWSER_OUTPUT_DIR | Directory to save HTML content | /app/data/html |
| BROWSER_SCRAPPER_IMPLEMENTATION | Browser scrapper implementation | src.services.browser_service,BrowserService |
| BROWSER_STORE_IMPLEMENTATION | Storage implementation | src.stores.browser_scrapper_store_path,BrowserScrapperStorePath |
| BROWSER_DELAY_IN_MILLISECONDS_MIN | Minimum delay between actions | 200 |
| BROWSER_DELAY_IN_MILLISECONDS_MAX | Maximum delay between actions | 1000 |
| BROWSER_PAGE_TIMEOUT_IN_MILLISECONDS | Timeout for page operations | 30000 |
| BROWSER_CLICK_TIMEOUT_IN_MILLISECONDS | Timeout for click operations | 5000 |
| BROWSER_MAX_CONCURRENT | Maximum concurrent searches | 2 |
| BOOKING_AREA | Default area to search | Psiri |
| BOOKING_GUESTS | Default number of guests | 2 |
| DISPLAY | X11 display for Xvfb | :99 |

## Building and Running the Service

### Building the Docker Image

First, build the Docker image:

```bash
# Build the image with a tag
docker build -t airprice-searcher .

# Build with a specific tag for versioning
docker build -t airprice-searcher:latest .
```

### Basic Usage

To start the service with default configuration:

**Linux/macOS (Bash):**
```bash
# Run the container with basic setup
docker run -d \
  --name searcher-service \
  -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  airprice-searcher
```

**Windows (PowerShell):**
```powershell
# Run the container with basic setup
docker run -d `
  --name searcher-service `
  -p 8000:8000 `
  -v "${PWD}/data:/app/data" `
  airprice-searcher
```

This will:
1. Start the container in detached mode (`-d`)
2. Name the container `searcher-service`
3. Map port 8000 to your host machine
4. Mount the local `data` directory for persistent storage

### Running with Custom Environment Variables

**Linux/macOS (Bash):**
```bash
# Run with custom configuration
docker run -d \
  --name searcher-service \
  -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  -e "BROWSER_HEADLESS=true" \
  -e "BOOKING_AREA=Psiri" \
  -e "BOOKING_GUESTS=4" \
  -e "BROWSER_MAX_CONCURRENT=1" \
  airprice-searcher
```

**Windows (PowerShell):**
```powershell
# Run with custom configuration
docker run -d `
  --name searcher-service `
  -p 8000:8000 `
  -v "${PWD}/data:/app/data" `
  -e "BROWSER_HEADLESS=true" `
  -e "BOOKING_AREA=Psiri" `
  -e "BOOKING_GUESTS=4" `
  -e "BROWSER_MAX_CONCURRENT=1" `
  airprice-searcher
```

### Viewing Logs

To view the logs:

```bash
# View logs for the running container
docker logs -f searcher-service

# Or use container ID
docker logs -f <container_id>
```

### Stopping the Service

To stop the service:

```bash
# Stop the named container
docker stop searcher-service

# Remove the container
docker rm searcher-service
```

## Docker Features

### Built-in Features

The Docker image includes several built-in features:

- **uv Package Manager**: Fast Python package management and dependency resolution
- **Xvfb Support**: Virtual display server for running browsers in headless environments
- **Playwright & Chromium**: Pre-installed browser automation tools
- **System Dependencies**: All required system packages for browser automation
- **Automatic Startup**: Xvfb starts automatically before the application

### Volume Mounting

The container supports mounting directories for:

- **Data Persistence**: Mount `/app/data` to persist downloaded HTML files
- **Configuration**: Mount custom `.env` files for configuration
- **Development**: Mount source code for development (see Development Mode below)

### Development Mode

For development, you can mount the source code directory to make changes without rebuilding:

**Linux/macOS (Bash):**
```bash
# Development mode with source code mounting
docker run -d \
  --name searcher-dev \
  -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/src:/app/src \
  -e "BROWSER_HEADLESS=false" \
  airprice-searcher
```

**Windows (PowerShell):**
```powershell
# Development mode with source code mounting
docker run -d `
  --name searcher-dev `
  -p 8000:8000 `
  -v "${PWD}/data:/app/data" `
  -v "${PWD}/src:/app/src" `
  -e "BROWSER_HEADLESS=false" `
  airprice-searcher
```

**Note**: Changes to dependencies in `pyproject.toml` will still require rebuilding the image.

## Troubleshooting

### Browser Issues

If you encounter issues with the browser automation:

1. **Xvfb Issues**: Check if the container has the necessary permissions to run Xvfb
   ```bash
   # Check container logs for Xvfb errors
   docker logs searcher-service | grep -i xvfb
   ```

2. **Headless Mode**: Try setting `BROWSER_HEADLESS=true` for environments without display support
   ```bash
   docker run -d -p 8000:8000 -e "BROWSER_HEADLESS=true" airprice-searcher
   ```

3. **Browser Dependencies**: Ensure all browser dependencies are installed (they should be in the image)
   ```bash
   # Check if Chromium is available
   docker exec searcher-service which chromium
   ```

### Connection Issues

If you can't connect to the API:

1. **Container Status**: Verify the container is running
   ```bash
   docker ps | grep searcher-service
   ```

2. **Port Conflicts**: Ensure port 8000 is not being used by another service
   ```bash
   # Check what's using port 8000
   netstat -tulpn | grep :8000
   # Or on macOS/Windows
   lsof -i :8000
   ```

3. **Container Logs**: Check the logs for startup errors
   ```bash
   docker logs searcher-service
   ```

4. **Network Issues**: Test if the API is responding
   ```bash
   curl http://localhost:8000/docs
   ```

### Performance Issues

If the service is running slowly:

1. **Resource Limits**: Check if the container has enough resources
   ```bash
   # Monitor container resource usage
   docker stats searcher-service
   ```

2. **Concurrent Searches**: Reduce the number of concurrent searches
   ```bash
   docker run -d -p 8000:8000 -e "BROWSER_MAX_CONCURRENT=1" airprice-searcher
   ```

### Data Persistence Issues

If downloaded data is not persisting:

1. **Volume Mounting**: Ensure the data directory is properly mounted
   ```bash
   # Check if volume is mounted correctly
   docker inspect searcher-service | grep -A 10 "Mounts"
   ```

2. **Permissions**: Check directory permissions
   ```bash
   # Ensure the data directory is writable
   ls -la $(pwd)/data
   ```

## Service Features

### Result Objects

The searcher service returns structured result objects that provide comprehensive information about search operations:

#### SearchResult

Individual searches return a `SearchResult` object with the following structure:

```json
{
  "area": "Psiri",
  "guests": 2,
  "period": "2025-06-01 to 2025-06-08",
  "success": true,
  "output_path": "/app/data/html/Psiri/2/2025-06-01_2025-06-08/20250127_103045.html",
  "elapsed_time": 27.34,
  "error": null
}
```

#### PeriodSearchResult

Period searches return a `PeriodSearchResult` object with the following structure:

```json
{
  "total_weeks": 4,
  "successful": 3,
  "failed": 1,
  "errors": ["Week 2/4 failed for 2025-06-08 to 2025-06-15: Connection timeout"],
  "elapsed_time": 89.67
}
```

### API Endpoints

When running the container, the following API endpoints are available at `http://localhost:8000`:

- **`POST /api/v1/search`**: Perform a single booking.com search
- **`POST /api/v1/search-period`**: Perform multiple searches across a date range
- **`POST /api/v1/download-apartment`**: Download a specific apartment page from Booking.com
- **`GET /api/v1/search/status`**: Get the status of all searches
- **`GET /api/v1/download-apartment/status`**: Get the status of all apartment downloads
- **`GET /docs`**: Interactive API documentation (Swagger UI)

#### Search Status Response

The `/api/v1/search/status` endpoint returns detailed information about all searches:

```json
{
  "searches": [
    {
      "id": "0",
      "area": "Psiri",
      "guests": "4",
      "period": "2025-06-01 to 2025-06-08",
      "status": "completed",
      "started_at": "2025-05-25T17:54:10.143450",
      "completed_at": "2025-05-25T17:55:35.209000",
      "output_path": "/app/data/html/Psiri/4/2025-06-01_2025-06-08/20250525_175535.html",
      "error": "",
      "elapsed_time": "85.066"
    },
    {
      "id": "1",
      "area": "Psiri",
      "guests": "2",
      "period": "2025-07-01 to 2025-07-08",
      "status": "in_progress",
      "started_at": "2025-05-25T17:54:31.874262",
      "completed_at": "",
      "output_path": "",
      "error": "",
      "elapsed_time": "45.123"
    }
  ]
}
```

**Status Values:**
- `in_progress`: Operation is currently running
- `completed`: Operation finished successfully with results saved
- `failed`: Operation encountered an error and could not complete

#### Apartment Download Status Response

The `/api/v1/download-apartment/status` endpoint returns detailed information about all apartment downloads:

```json
{
  "downloads": [
    {
      "apartment_id": "amazing-apartment",
      "url": "https://www.booking.com/hotel/gr/amazing-apartment.html",
      "status": "completed",
      "started_at": "2025-06-01T13:20:20.553000",
      "completed_at": "2025-06-01T13:20:43.743000",
      "output_path": "data/html/apartments/amazing-apartment/20250601_132043.html"
    },
    {
      "apartment_id": "another-apartment",
      "url": "https://www.booking.com/hotel/gr/another-apartment.html",
      "status": "in_progress",
      "started_at": "2025-06-01T13:25:10.123000",
      "completed_at": "",
      "output_path": ""
    }
  ]
}
```

**Apartment Download Features:**
- **URL Validation**: Only accepts valid Booking.com apartment URLs
- **Dual Format Storage**: Saves both HTML (raw) and JSON (parsed) files
- **Data Extraction**: Extracts apartment details using integrated parser
- **Organized Storage**: Files saved in `apartments/{apartment_id}/` directories

### Performance Monitoring

The service provides detailed performance metrics through the result objects:

- **Elapsed Time**: All operations include timing information
- **Success/Failure Tracking**: Clear indication of operation outcomes
- **Error Details**: Specific error messages for failed operations
- **Batch Processing**: Period searches provide summary statistics

### Timeout Configuration

The service includes comprehensive timeout management for different browser operations:

- **Page Timeout** (`BROWSER_PAGE_TIMEOUT_IN_MILLISECONDS`): Default 30000ms for page loading and navigation
- **Click Timeout** (`BROWSER_CLICK_TIMEOUT_IN_MILLISECONDS`): Default 5000ms for click interactions
- **Unified Implementation**: All browser classes use consistent timeout configuration

Example Docker configuration with custom timeouts:
```bash
docker run -d \
  --name searcher-service \
  -p 8000:8000 \
  -e "BROWSER_PAGE_TIMEOUT_IN_MILLISECONDS=30000" \
  -e "BROWSER_CLICK_TIMEOUT_IN_MILLISECONDS=5000" \
  airprice-searcher
```

## Custom Configuration

### Using Environment Files

Create a custom `.env` file based on `.env.example`:

**Linux/macOS (Bash):**
```bash
# Create custom configuration
cp .env.example .env.custom

# Edit the configuration
nano .env.custom

# Run with custom environment file
docker run -d \
  --name searcher-service \
  -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  --env-file .env.custom \
  airprice-searcher
```

**Windows (PowerShell):**
```powershell
# Create custom configuration
Copy-Item .env.example .env.custom

# Edit the configuration (opens in default editor)
notepad .env.custom

# Run with custom environment file
docker run -d `
  --name searcher-service `
  -p 8000:8000 `
  -v "${PWD}/data:/app/data" `
  --env-file .env.custom `
  airprice-searcher
```

### Azure Container Registry

For production deployments, you can use the pre-built image from Azure Container Registry:

**Linux/macOS (Bash):**
```bash
# Login to Azure Container Registry (if you have access)
az acr login --name airprice

# Pull and run the image
docker run -d \
  --name searcher-service \
  -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  airprice.azurecr.io/airprice-searcher:latest
```

**Windows (PowerShell):**
```powershell
# Login to Azure Container Registry (if you have access)
az acr login --name airprice

# Pull and run the image
docker run -d `
  --name searcher-service `
  -p 8000:8000 `
  -v "${PWD}/data:/app/data" `
  airprice.azurecr.io/airprice-searcher:latest
```

## Container Management

### Useful Commands

```bash
# List all containers
docker ps -a

# View container resource usage
docker stats

# Execute commands inside the container
docker exec -it searcher-service bash

# Copy files from container
docker cp searcher-service:/app/data/html ./downloaded-data

# Restart the container
docker restart searcher-service

# View container configuration
docker inspect searcher-service
```
