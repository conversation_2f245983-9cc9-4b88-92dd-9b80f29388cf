"""
Apartment Download Service

This module provides the main service for downloading individual apartment pages
from Booking.com.
"""

import logging
import time
from datetime import datetime
from typing import List, Dict, Any

from src.factories.browser_scrapper_factory import BrowserScrapperFactory
from src.models.apartment_download_request import ApartmentDownloadRequest
from src.models.apartment_download_result import ApartmentDownloadResult

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ApartmentDownloadService:
    """
    Service for downloading individual apartment pages from Booking.com.
    """

    def __init__(self):
        """
        Initialize the apartment download service.
        """
        self.factory = BrowserScrapperFactory()
        self.download_statuses = []

    async def download_apartment(
        self, 
        request: ApartmentDownloadRequest, 
        output_dir: str
    ) -> ApartmentDownloadResult:
        """
        Download an apartment page and save the results.

        Args:
            request: The apartment download request
            output_dir: The directory to save the results to

        Returns:
            ApartmentDownloadResult: The result of the download operation
        """
        start_time = time.time()
        apartment_id = request.get_apartment_id()

        # Add status tracking
        status_index = len(self.download_statuses)
        self.download_statuses.append({
            "apartment_id": apartment_id,
            "url": request.url,
            "status": "in_progress",
            "started_at": datetime.now().isoformat(),
            "output_path": "",
        })

        logger.info(f"Starting apartment download for {request.get_display_name()}")

        try:
            # Create apartment scraper using the factory
            apartment_scraper = self.factory.create_apartment_scraper(request, output_dir)
            output_path = await apartment_scraper.download_page(request.get_sub_directory())

            # Update status
            self.download_statuses[status_index].update({
                "status": "completed",
                "completed_at": datetime.now().isoformat(),
                "output_path": output_path or "",
            })

            # Publish event to notify other services (if needed)
            await self._publish_download_completed_event(request, output_path)

            # Calculate elapsed time and create result
            elapsed_time = time.time() - start_time
            result = ApartmentDownloadResult(
                url=request.url,
                apartment_id=apartment_id,
                success=True,
                output_path=output_path,
                elapsed_time=elapsed_time
            )

            logger.info(f"Apartment download completed successfully in {elapsed_time:.2f} seconds")
            return result

        except Exception as e:
            # Update status
            self.download_statuses[status_index].update({
                "status": "failed",
                "completed_at": datetime.now().isoformat(),
                "error": str(e),
            })

            # Calculate elapsed time and create error result
            elapsed_time = time.time() - start_time
            result = ApartmentDownloadResult(
                url=request.url,
                apartment_id=apartment_id,
                success=False,
                output_path=None,
                elapsed_time=elapsed_time,
                error=str(e)
            )

            logger.error(f"Apartment download failed after {elapsed_time:.2f} seconds: {e}")
            return result

    async def _publish_download_completed_event(
        self, 
        request: ApartmentDownloadRequest, 
        output_path: str
    ):
        """
        Publish an event when an apartment download is completed.
        
        This can be used to notify other services about completed downloads.
        Currently a placeholder for future event publishing functionality.

        Args:
            request: The apartment download request
            output_path: The path where the content was saved
        """
        # Placeholder for event publishing
        # In the future, this could publish to a message queue, webhook, etc.
        logger.debug(f"Download completed event: {request.url} -> {output_path}")

    def get_download_statuses(self) -> List[Dict[str, Any]]:
        """
        Get the status of all apartment downloads.

        Returns:
            List[Dict[str, Any]]: List of download statuses
        """
        return self.download_statuses.copy()

    def get_download_status(self, apartment_id: str) -> Dict[str, Any]:
        """
        Get the status of a specific apartment download.

        Args:
            apartment_id: The apartment ID to get status for

        Returns:
            Dict[str, Any]: The download status, or None if not found
        """
        for status in self.download_statuses:
            if status.get("apartment_id") == apartment_id:
                return status.copy()
        return None

    def clear_download_statuses(self):
        """
        Clear all download statuses.
        """
        self.download_statuses.clear()
        logger.info("Cleared all download statuses")
