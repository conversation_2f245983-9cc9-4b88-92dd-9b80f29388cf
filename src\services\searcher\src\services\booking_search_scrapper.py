"""
Booking Search Scrapper

This module provides a scrapper for booking.com search results.
"""

import json
import logging
import os
import random

from src.models.booking_search_request import BookingSearchRequest
from src.parsers.booking_results_extractor import BookingResultsExtractor
from src.services.browser_service import BrowserService
from src.stores.browser_scrapper_store_abstract import BrowserScrapperStoreAbstract

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Booking.com base URL
BOOKING_BASE_URL = "https://www.booking.com"


class BookingSearchScrapper:
    """
    Scrapper for booking.com search results.
    """

    def __init__(
        self, request: BookingSearchRequest, store: BrowserScrapperStoreAbstract
    ) -> None:
        """
        Initialize the BookingSearchScrapper with a request and a store.

        Args:
            request: The booking search request
            store: The store to save the results to
        """
        self.url = BOOKING_BASE_URL
        self.store = store
        self.request = request
        self.browser_service = BrowserService()
        self.results_extractor = BookingResultsExtractor()

    async def download_page(self, sub_dir: str = None) -> str:
        """
        Download the search results page and save it to the store.

        Args:
            sub_dir: The subdirectory to save the results to

        Returns:
            str: The path to the saved file
        """
        logger.info(
            f"Starting search for {self.request.area} with {self.request.guests} guests "
            f"from {self.request.check_in} to {self.request.check_out}"
        )

        try:
            # Perform the search and get the content
            content = await self.download_page_content()

            # Save the content
            output_path = self._save_content(content, sub_dir)

            logger.info(f"Search completed and saved to {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Error during search: {e}")
            raise

    async def download_page_content(self) -> str:
        """
        Download the search results page content.

        Returns:
            str: The HTML content of the search results page
        """
        try:
            # Use the browser service to perform the search
            content = await self.browser_service.get_booking_content(self.request)
            return content

        except Exception as e:
            logger.error(f"Error downloading page content: {e}")
            raise

    def _save_content(self, content: str, sub_dir: str = None) -> str:
        """
        Save the content to the store.

        Args:
            content: The content to save
            sub_dir: The subdirectory to save the content to

        Returns:
            str: The path to the saved file
        """
        save_result = self.store.save_content(self.url, content, ".html", sub_dir)
        extracted_content = self.results_extractor.process_html_content(content)
        json_content = json.dumps(extracted_content, indent=2)
        save_result = self.store.save_content(
            self.url, json_content, ".json", sub_dir
        )
        return save_result

    @property
    def delay_milliseconds(self) -> float:
        """
        Get the delay in milliseconds.

        Returns:
            float: The delay in milliseconds
        """
        min_delay = float(os.getenv("BROWSER_DELAY_IN_MILLISECONDS_MIN", "200"))
        max_delay = float(os.getenv("BROWSER_DELAY_IN_MILLISECONDS_MAX", "1000"))
        return random.uniform(min_delay, max_delay)
