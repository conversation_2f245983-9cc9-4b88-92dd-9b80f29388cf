["tests/test_apartment_download.py::TestApartmentDownloadRequest::test_get_apartment_id_from_path", "tests/test_apartment_download.py::TestApartmentDownloadRequest::test_get_apartment_id_from_query_param", "tests/test_apartment_download.py::TestApartmentDownloadRequest::test_get_display_name", "tests/test_apartment_download.py::TestApartmentDownloadRequest::test_get_sub_directory", "tests/test_apartment_download.py::TestApartmentDownloadRequest::test_invalid_urls", "tests/test_apartment_download.py::TestApartmentDownloadRequest::test_valid_booking_url", "tests/test_apartment_download.py::TestApartmentDownloadResult::test_failed_result", "tests/test_apartment_download.py::TestApartmentDownloadResult::test_successful_result", "tests/test_apartment_download.py::TestApartmentDownloadResult::test_to_dict", "tests/test_apartment_download.py::TestApartmentDownloadService::test_clear_download_statuses", "tests/test_apartment_download.py::TestApartmentDownloadService::test_get_download_statuses_empty", "tests/test_apartment_download.py::TestApartmentDownloadService::test_service_initialization", "tests/test_api_error_handling.py::TestAPIErrorHandling::test_search_status_endpoint_returns_error_info", "tests/test_api_error_handling.py::TestAPIErrorHandling::test_search_status_includes_elapsed_time", "tests/test_api_error_handling.py::TestAPIErrorHandling::test_search_status_response_model_validation", "tests/test_api_error_handling.py::TestAPIErrorHandling::test_successful_search_status_no_error", "tests/test_search_service_error_handling.py::TestSearchServiceErrorHandling::test_multiple_searches_with_mixed_results", "tests/test_search_service_error_handling.py::TestSearchServiceErrorHandling::test_search_error_updates_status", "tests/test_search_service_error_handling.py::TestSearchServiceErrorHandling::test_search_status_elapsed_time_calculation", "tests/test_search_service_error_handling.py::TestSearchServiceErrorHandling::test_search_success_no_error"]